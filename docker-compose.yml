version: '3.8'

services:
  next-agent:
    build: .
    ports:
      - "3000:3000"  # MCP Server
      - "3001:3001"  # RSS Server
    environment:
      - NODE_ENV=production
      - RSS_PORT=3001
      - RSS_BASE_URL=http://localhost:3001
      - RSS_SITE_NAME=RSS Content Feed
      - RSS_SITE_DESCRIPTION=Curated content feed powered by MCP tools
      - RSS_AUTHOR_EMAIL=<EMAIL>
      - DATABASE_PATH=rss-content.db
      # Add your OpenAI API key for LLM evaluation (optional)
      # - OPENAI_API_KEY=your_openai_api_key_here
    volumes:
      # Persist the SQLite database
      - ./data:/app/data
    restart: unless-stopped

  # Optional: Add a simple web interface for RSS feeds
  rss-viewer:
    image: nginx:alpine
    ports:
      - "8080:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - next-agent
    restart: unless-stopped
