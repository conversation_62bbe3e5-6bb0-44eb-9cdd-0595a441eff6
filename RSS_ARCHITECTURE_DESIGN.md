# RSS增强系统MVP架构设计文档

## 1. 系统概述

RSS增强系统是基于现有Next Agent MCP服务器的扩展，通过聚合BlackHatWorld、GitHub、DeepWiki三个数据源的优质内容，生成标准RSS Feed输出。

### 1.1 核心特性
- **手动触发更新** - 简化MVP实现，避免复杂的调度系统
- **多源内容聚合** - 整合三个不同类型的内容源
- **智能价值评估** - 基于各源特有指标评估内容质量
- **标准RSS输出** - 生成符合RSS 2.0标准的XML格式
- **轻量级存储** - 使用SQLite进行本地数据持久化

### 1.2 技术约束
- 基于Bun + TypeScript + FastMCP技术栈
- 复用现有三层架构模式（index/client/parser）
- 支持stdio和SSE两种MCP传输方式
- 零额外依赖，使用Bun内置功能

## 2. 系统架构

### 2.1 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CLI工具调用   │    │   HTTP API调用  │    │   RSS Feed输出  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   RSS管理器     │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   内容聚合器    │
                    └─────────────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                       │                        │
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│BlackHatWorld│    │   GitHub    │    │  DeepWiki   │
│    工具     │    │    工具     │    │    工具     │
└─────────────┘    └─────────────┘    └─────────────┘
        │                       │                        │
        └────────────────────────┼────────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   价值评估器    │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   SQLite数据库  │
                    └─────────────────┘
```

### 2.2 核心模块结构

```
src/tools/rss/
├── index.ts          # MCP工具入口，定义rss-update、rss-generate工具
├── client.ts         # RSS客户端，处理业务逻辑协调
├── parser.ts         # RSS解析器，生成标准RSS XML
├── database.ts       # 数据库操作层，SQLite CRUD操作
├── aggregator.ts     # 内容聚合器，调用现有MCP工具
├── evaluator.ts      # 价值评估器，内容质量评分和去重
├── types.ts          # TypeScript类型定义
├── config.ts         # 配置管理
├── monitor.ts        # 监控和统计
└── utils.ts          # 工具函数
```

## 3. 数据流程设计

### 3.1 内容更新流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant RSS as RSS工具
    participant Agg as 内容聚合器
    participant BHW as BlackHatWorld
    participant GH as GitHub
    participant DW as DeepWiki
    participant Eval as 价值评估器
    participant DB as SQLite数据库
    
    User->>RSS: rss-update命令
    RSS->>Agg: 启动内容聚合
    
    par 并行获取内容
        Agg->>BHW: 搜索热门帖子
        BHW-->>Agg: 返回帖子数据
    and
        Agg->>GH: 搜索优质仓库
        GH-->>Agg: 返回仓库数据
    and
        Agg->>DW: 搜索代码片段
        DW-->>Agg: 返回代码数据
    end
    
    Agg->>Eval: 标准化内容格式
    Eval->>Eval: 计算质量评分
    Eval->>Eval: 内容去重处理
    Eval->>DB: 保存到数据库
    DB-->>RSS: 返回保存结果
    RSS-->>User: 返回更新统计
```

### 3.2 RSS生成流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant RSS as RSS工具
    participant DB as SQLite数据库
    participant Parser as RSS解析器
    
    User->>RSS: rss-generate命令
    RSS->>DB: 查询最新内容
    DB-->>RSS: 返回内容列表
    RSS->>Parser: 格式化RSS内容
    Parser->>Parser: 生成RSS XML
    Parser-->>User: 返回RSS Feed
```

## 4. 技术实现细节

### 4.1 数据库设计

```sql
-- 内容表
CREATE TABLE contents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source TEXT NOT NULL,           -- 'blackhatworld', 'github', 'deepwiki'
    source_id TEXT NOT NULL,        -- 原始内容ID
    title TEXT NOT NULL,
    description TEXT,
    url TEXT NOT NULL,
    content_data JSON,              -- 存储完整的原始数据
    quality_score REAL DEFAULT 0,   -- 质量评分 0-100
    content_hash TEXT UNIQUE,       -- 内容哈希，用于去重
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(source, source_id)
);

-- 索引优化
CREATE INDEX idx_contents_source ON contents(source);
CREATE INDEX idx_contents_quality ON contents(quality_score DESC);
CREATE INDEX idx_contents_created ON contents(created_at DESC);
```

### 4.2 价值评估算法

#### BlackHatWorld评分算法
```typescript
function calculateBHWScore(content: BHWContent): number {
  const replyScore = Math.min(50, content.replies * 2);
  const viewScore = Math.min(30, content.views / 100);
  const authorScore = content.author.reputation > 100 ? 20 : 10;
  return Math.round(replyScore + viewScore + authorScore);
}
```

#### GitHub评分算法
```typescript
function calculateGitHubScore(content: GitHubContent): number {
  const starScore = Math.min(60, Math.log10(content.stars + 1) * 15);
  const forkScore = Math.min(20, Math.log10(content.forks + 1) * 10);
  const activityScore = isRecentlyActive(content.pushedAt) ? 20 : 10;
  return Math.round(starScore + forkScore + activityScore);
}
```

#### DeepWiki评分算法
```typescript
function calculateDeepWikiScore(content: DeepWikiContent): number {
  const snippetScore = Math.min(40, content.codeSnippets.length * 10);
  const qualityScore = content.hasDocumentation ? 30 : 20;
  const repoScore = getRepoPopularity(content.repository) * 0.3;
  return Math.round(snippetScore + qualityScore + repoScore);
}
```

### 4.3 RSS XML模板

```xml
<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:content="http://purl.org/rss/1.0/modules/content/">
  <channel>
    <title>Next Agent RSS Feed</title>
    <description>聚合BlackHatWorld、GitHub、DeepWiki的优质内容</description>
    <link>http://localhost:3000/rss</link>
    <lastBuildDate>{lastBuildDate}</lastBuildDate>
    <generator>Next Agent RSS v1.0</generator>
    
    {items.map(item => `
    <item>
      <title><![CDATA[${item.title}]]></title>
      <description><![CDATA[${item.description}]]></description>
      <link>${item.url}</link>
      <pubDate>${item.pubDate}</pubDate>
      <guid isPermaLink="false">${item.guid}</guid>
      <category>${item.source}</category>
    </item>`)}
  </channel>
</rss>
```

## 5. 集成方案

### 5.1 MCP工具集成

在现有的`src/stdio.ts`和`src/sse.ts`中添加RSS工具：

```typescript
// 导入RSS工具
import { rssUpdateTool, rssGenerateTool } from "./tools/rss/index";

// 注册工具
server.addTool(rssUpdateTool);
server.addTool(rssGenerateTool);
```

### 5.2 HTTP接口扩展

扩展SSE服务器，提供HTTP访问接口：

```typescript
// 在src/sse.ts中添加自定义路由
server.start({
  transportType: "httpStream",
  httpStream: {
    port: 3000,
    customRoutes: {
      '/rss': handleRSSRequest,
      '/rss/update': handleUpdateRequest
    }
  }
});
```

### 5.3 现有工具复用

直接调用现有MCP工具的execute方法：

```typescript
// 复用BlackHatWorld工具
const bhwResult = await blackhatWorldSearchTool.execute({
  keywords: topics.join(' '),
  pages: 2,
  minReplies: 10
});

// 复用GitHub工具
const githubResult = await githubSearchTool.execute({
  query: topics.join(' OR '),
  stars: '>100',
  limit: 20
});

// 复用DeepWiki工具
const deepwikiResult = await deepWikiSearchTool.execute({
  query: topics[0],
  repository: 'facebook/react',
  focus: 'code'
});
```

## 6. 扩展性设计

### 6.1 插件化内容源

```typescript
// 抽象内容源接口
abstract class ContentSource {
  abstract name: string;
  abstract fetchContent(params: FetchParams): Promise<RawContent[]>;
  abstract calculateQuality(content: RawContent): number;
}

// 源管理器
class SourceManager {
  private sources = new Map<string, ContentSource>();
  
  registerSource(source: ContentSource): void {
    this.sources.set(source.name, source);
  }
}
```

### 6.2 配置化评估策略

```typescript
// 可配置的评估参数
interface EvaluationConfig {
  blackhatworld: {
    replyWeight: number;
    viewWeight: number;
    minReplies: number;
  };
  github: {
    starWeight: number;
    activityWeight: number;
    minStars: number;
  };
  deepwiki: {
    snippetWeight: number;
    qualityWeight: number;
  };
}
```

### 6.3 监控和统计

```typescript
// 运行时统计
interface RSSStats {
  totalUpdates: number;
  successfulUpdates: number;
  lastUpdateTime: Date;
  sourceStats: Record<string, SourceStats>;
}
```

## 7. 实施计划

### 7.1 开发阶段

#### 阶段1：基础架构 (2-3天)
- [ ] 创建RSS工具目录结构
- [ ] 实现基础类型定义
- [ ] 设置SQLite数据库和表结构
- [ ] 实现基础的MCP工具接口

#### 阶段2：内容聚合 (3-4天)
- [ ] 实现内容聚合器
- [ ] 集成现有三个MCP工具
- [ ] 实现内容标准化处理
- [ ] 添加错误处理和重试机制

#### 阶段3：价值评估 (2-3天)
- [ ] 实现价值评估算法
- [ ] 实现内容去重逻辑
- [ ] 添加质量评分计算
- [ ] 实现数据库存储逻辑

#### 阶段4：RSS生成 (2天)
- [ ] 实现RSS XML生成器
- [ ] 实现RSS解析器
- [ ] 添加HTTP接口支持
- [ ] 实现缓存机制

#### 阶段5：集成测试 (2天)
- [ ] 集成到现有MCP服务器
- [ ] 端到端功能测试
- [ ] 性能优化
- [ ] 文档完善

### 7.2 测试策略

#### 单元测试
- 价值评估算法测试
- 内容去重逻辑测试
- RSS XML生成测试
- 数据库操作测试

#### 集成测试
- MCP工具集成测试
- 多源内容聚合测试
- HTTP接口测试
- 错误处理测试

#### 性能测试
- 大量数据处理性能
- 并发请求处理能力
- 内存使用优化
- 数据库查询优化

### 7.3 部署和维护

#### 部署要求
- Bun运行时环境
- SQLite数据库文件权限
- HTTP端口访问权限
- 足够的磁盘空间用于数据存储

#### 维护任务
- 定期清理过期数据
- 监控系统性能指标
- 更新内容源适配器
- 优化评估算法参数

## 8. 风险评估和缓解策略

### 8.1 技术风险

**风险：外部API限制**
- 缓解：实现请求频率控制和缓存机制
- 缓解：添加多个API密钥轮换使用

**风险：数据质量问题**
- 缓解：实现多层内容验证
- 缓解：添加人工审核机制

**风险：性能瓶颈**
- 缓解：实现异步处理和批量操作
- 缓解：添加内存和磁盘缓存

### 8.2 业务风险

**风险：内容版权问题**
- 缓解：只存储元数据和摘要
- 缓解：提供原始链接而非完整内容

**风险：内容时效性**
- 缓解：实现增量更新机制
- 缓解：添加内容过期清理

## 9. 总结

