# Docker Configuration for RSS System

This document explains how to run the RSS system using Docker.

## Quick Start

### Using Docker Compose (Recommended)

1. **Start the services:**
   ```bash
   docker-compose up -d
   ```

2. **Add sample data:**
   ```bash
   docker-compose exec next-agent bun src/test-data.ts
   ```

3. **Access the services:**
   - MCP Server: http://localhost:3000
   - RSS Server: http://localhost:3001
   - RSS Viewer: http://localhost:8080

### Using Docker Build

1. **Build the image:**
   ```bash
   docker build -t next-agent-rss .
   ```

2. **Run the container:**
   ```bash
   docker run -d \
     --name next-agent-rss \
     -p 3000:3000 \
     -p 3001:3001 \
     -e RSS_PORT=3001 \
     -e RSS_BASE_URL=http://localhost:3001 \
     -v $(pwd)/data:/app/data \
     next-agent-rss
   ```

## Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `RSS_PORT` | 3001 | Port for RSS HTTP server |
| `RSS_BASE_URL` | http://localhost:3001 | Base URL for RSS feeds |
| `RSS_SITE_NAME` | RSS Content Feed | Site name for RSS feeds |
| `RSS_SITE_DESCRIPTION` | Curated content feed | Site description |
| `RSS_AUTHOR_EMAIL` | <EMAIL> | Author email for feeds |
| `DATABASE_PATH` | rss-content.db | SQLite database file path |
| `OPENAI_API_KEY` | - | OpenAI API key for content evaluation |

## Exposed Ports

- **3000**: MCP Server (FastMCP tools)
- **3001**: RSS HTTP Server (RSS feeds)

## Volume Mounts

- `/app/data`: Persistent storage for SQLite database

## Health Checks

The container includes a health check that verifies the RSS server is responding:

```bash
curl -f http://localhost:3001/health
```

## Testing RSS Feeds

Once the container is running, you can test the RSS feeds:

```bash
# Health check
curl http://localhost:3001/health

# Main RSS feed
curl http://localhost:3001/feed

# Category-specific feed
curl http://localhost:3001/feed/tech

# With query parameters
curl "http://localhost:3001/feed?limit=5&include_content=false"
```

## RSS Viewer Interface

Access the web interface at http://localhost:8080 to:
- View available RSS feeds
- Check server status
- Browse feed categories

## Logs

View container logs:

```bash
# Docker Compose
docker-compose logs -f next-agent

# Docker
docker logs -f next-agent-rss
```

## Stopping Services

```bash
# Docker Compose
docker-compose down

# Docker
docker stop next-agent-rss
docker rm next-agent-rss
```

## Production Deployment

For production deployment:

1. Set proper environment variables
2. Use a reverse proxy (nginx/traefik)
3. Configure SSL/TLS certificates
4. Set up monitoring and logging
5. Use persistent volumes for database storage

Example production docker-compose.yml:

```yaml
version: '3.8'
services:
  next-agent:
    build: .
    environment:
      - NODE_ENV=production
      - RSS_BASE_URL=https://your-domain.com
      - RSS_SITE_NAME=Your RSS Feed
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - rss_data:/app/data
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.rss.rule=Host(`your-domain.com`)"
      - "traefik.http.services.rss.loadbalancer.server.port=3001"

volumes:
  rss_data:
```
