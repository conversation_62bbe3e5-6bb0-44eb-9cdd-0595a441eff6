/**
 * Content Writer MCP Tool
 * Provides MCP interface for storing discovered content
 */

import { z } from "zod";
import { getContentWriter } from "./content-writer-core.js";
import type { ContentItem } from "./types.js";

// Input schema for content writing
const contentWriterSchema = z.object({
  title: z.string().min(1).describe("Content title (required)"),
  description: z.string().optional().describe("Content description or summary"),
  content: z.string().optional().describe("Full content text"),
  url: z.string().url().describe("Source URL (must be valid URL)"),
  author: z.string().optional().describe("Content author"),
  published_date: z.string().optional().describe("Publication date (ISO format)"),
  source: z.string().min(1).describe("Source identifier (e.g., 'github-search', 'blackhatworld', 'manual')"),
  category: z.string().optional().describe("Content category name (will be created if doesn't exist)"),
  tags: z.array(z.string()).optional().describe("Content tags"),
  evaluate_quality: z.boolean().default(true).describe("Whether to evaluate content quality using LLM"),
  auto_approve: z.boolean().default(false).describe("Auto-approve high-quality content (quality_score >= 0.7)")
});

// Batch writing schema
const batchContentWriterSchema = z.object({
  items: z.array(z.object({
    title: z.string().min(1),
    description: z.string().optional(),
    content: z.string().optional(),
    url: z.string().url(),
    author: z.string().optional(),
    published_date: z.string().optional(),
    source: z.string().min(1),
    tags: z.array(z.string()).optional()
  })).min(1).max(10).describe("Array of content items to write (max 10 items)"),
  category: z.string().optional().describe("Category for all items"),
  evaluate_quality: z.boolean().default(true).describe("Whether to evaluate content quality using LLM"),
  auto_approve: z.boolean().default(false).describe("Auto-approve high-quality content")
});

// URL-based content writing schema
const urlContentWriterSchema = z.object({
  url: z.string().url().describe("URL to extract content from"),
  source: z.string().min(1).describe("Source identifier"),
  title: z.string().optional().describe("Override title (if not provided, will use URL hostname)"),
  description: z.string().optional().describe("Content description"),
  content: z.string().optional().describe("Content text"),
  author: z.string().optional().describe("Content author"),
  category: z.string().optional().describe("Content category"),
  tags: z.array(z.string()).optional().describe("Content tags"),
  evaluate_quality: z.boolean().default(true).describe("Whether to evaluate content quality using LLM"),
  auto_approve: z.boolean().default(false).describe("Auto-approve high-quality content")
});

// Content status update schema
const contentStatusSchema = z.object({
  content_id: z.number().int().positive().describe("Content ID to update"),
  status: z.enum(['pending', 'approved', 'rejected']).describe("New status for the content")
});

// ============================================================================
// Tool Implementations
// ============================================================================

/**
 * Main content writer tool
 */
export const contentWriterTool = {
  name: "rss-content-writer",
  description: "Store discovered content in RSS database with optional LLM quality evaluation. Supports automatic categorization and quality scoring.",
  parameters: contentWriterSchema,
  execute: async (args: z.infer<typeof contentWriterSchema>) => {
    const writer = getContentWriter();

    try {
      const { title, description, content, url, author, published_date, source, category, tags, evaluate_quality, auto_approve } = args;

      const contentItem: Omit<ContentItem, 'id' | 'created_at' | 'updated_at'> = {
        title,
        description,
        content,
        url,
        author,
        published_date: published_date ? new Date(published_date).toISOString() : new Date().toISOString(),
        source,
        tags
      };

      const result = await writer.writeContent(contentItem, {
        evaluate_quality,
        auto_approve,
        category_name: category,
        tags: tags || []
      });

      return `✅ Content saved successfully!
📝 Title: ${result.title}
🆔 ID: ${result.id}
🔗 URL: ${result.url}
📂 Category ID: ${result.category_id || 'N/A'}
⭐ Quality Score: ${result.quality_score ? result.quality_score.toFixed(2) : 'N/A'}
📊 Status: ${result.status}

Content "${result.title}" has been stored in the RSS database.`;

    } catch (error) {
      console.error("❌ Content writer tool failed:", error);
      return `❌ Failed to save content: ${error instanceof Error ? error.message : "Unknown error occurred"}`;
    }
  }
};

/**
 * Batch content writer tool
 */
export const batchContentWriterTool = {
  name: "rss-batch-content-writer",
  description: "Write multiple content items to RSS database in batch. Useful for bulk content import.",
  parameters: batchContentWriterSchema,
  execute: async (args: z.infer<typeof batchContentWriterSchema>) => {
    const writer = getContentWriter();

    try {
      const { items, category, evaluate_quality, auto_approve } = args;

      const contentItems = items.map(item => ({
        ...item,
        published_date: item.published_date ? new Date(item.published_date).toISOString() : new Date().toISOString()
      }));

      const results = await writer.writeContentBatch(contentItems, {
        evaluate_quality,
        auto_approve,
        category_name: category
      });

      return `✅ Batch content write completed!
📊 Total items: ${items.length}
✅ Saved items: ${results.length}
❌ Failed items: ${items.length - results.length}
🆔 Content IDs: ${results.map(r => r.id).join(', ')}

Batch write completed: ${results.length}/${items.length} items saved successfully.`;

    } catch (error) {
      console.error("❌ Batch content writer tool failed:", error);
      return `❌ Failed to save batch content: ${error instanceof Error ? error.message : "Unknown error occurred"}`;
    }
  }
};

/**
 * URL-based content writer tool
 */
export const urlContentWriterTool = {
  name: "rss-url-content-writer",
  description: "Store content from URL in RSS database. Useful for web scraping integrations.",
  parameters: urlContentWriterSchema,
  execute: async (args: z.infer<typeof urlContentWriterSchema>) => {
    const writer = getContentWriter();

    try {
      const { url, source, title, description, content, author, category, tags, evaluate_quality, auto_approve } = args;

      const result = await writer.writeContentFromUrl(url, source, {
        title,
        description,
        content,
        author,
        evaluate_quality,
        auto_approve,
        category_name: category,
        tags
      });

      return `✅ URL content saved successfully!
📝 Title: ${result.title}
🆔 ID: ${result.id}
🔗 URL: ${result.url}
⭐ Quality Score: ${result.quality_score ? result.quality_score.toFixed(2) : 'N/A'}
📊 Status: ${result.status}

URL content "${result.title}" has been stored in the RSS database.`;

    } catch (error) {
      console.error("❌ URL content writer tool failed:", error);
      return `❌ Failed to save URL content: ${error instanceof Error ? error.message : "Unknown error occurred"}`;
    }
  }
};

/**
 * Content status update tool
 */
export const contentStatusTool = {
  name: "rss-content-status",
  description: "Update content status (approve/reject content for RSS feeds).",
  parameters: contentStatusSchema,
  execute: async (args: z.infer<typeof contentStatusSchema>) => {
    const writer = getContentWriter();

    try {
      const { content_id, status } = args;

      await writer.updateContentStatus(content_id, status);

      return `✅ Content status updated successfully!
🆔 Content ID: ${content_id}
📊 New Status: ${status}

Content ${content_id} status has been updated to: ${status}`;

    } catch (error) {
      console.error("❌ Content status update failed:", error);
      return `❌ Failed to update content status: ${error instanceof Error ? error.message : "Unknown error occurred"}`;
    }
  }
};

/**
 * Content statistics tool
 */
export const contentStatsTool = {
  name: "rss-content-stats",
  description: "Get RSS content database statistics and overview.",
  parameters: z.object({}),
  execute: async () => {
    const writer = getContentWriter();

    try {
      const stats = writer.getContentStats();

      return `📊 RSS Content Database Statistics

📈 Total Content: ${stats.total}
⏳ Pending: ${stats.pending}
✅ Approved: ${stats.approved}
❌ Rejected: ${stats.rejected}

📝 Content by Source:
${Object.entries(stats.by_source).map(([source, count]) => `  • ${source}: ${count}`).join('\n')}

Database contains ${stats.total} total content items.`;

    } catch (error) {
      console.error("❌ Content stats retrieval failed:", error);
      return `❌ Failed to retrieve content statistics: ${error instanceof Error ? error.message : "Unknown error occurred"}`;
    }
  }
};
