#!/usr/bin/env bun
import { FastMCP } from "fastmcp";
import { blackhatWorldScrapeTool, blackhatWorldSearchTool, blackhatWorldTrendingTool } from "./tools/blacthatworld/index";
import { githubSearchTool, githubBatchSearchTool } from "./tools/github/index";
import { deepWikiSearchTool, deepWikiBatchSearchTool } from "./tools/deepwiki/index";
import {
    contentWriterTool,
    batchContentWriterTool,
    urlContentWriterTool,
    contentStatusTool,
    contentStatsTool
} from "./tools/rss/index";

const server = new FastMCP({
    name: "next-mcp-server",
    version: "1.0.0"
});
// Add all tools to the server
server.addTool(blackhatWorldScrapeTool);
server.addTool(blackhatWorldSearchTool);
server.addTool(blackhatWorldTrendingTool);
server.addTool(githubSearchTool);
server.addTool(githubBatchSearchTool);
server.addTool(deepWikiSearchTool);
server.addTool(deepWikiBatchSearchTool);

// Add RSS tools
server.addTool(contentWriterTool);
server.addTool(batchContentWriterTool);
server.addTool(urlContentWriterTool);
server.addTool(contentStatusTool);
server.addTool(contentStatsTool);


server.start({
    transportType: "httpStream",
    httpStream: {
        port: 3000,
    },
});

