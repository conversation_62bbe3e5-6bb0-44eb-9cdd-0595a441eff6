#!/usr/bin/env bun
/**
 * RSS HTTP Server
 * Provides RSS feed endpoints for external consumption
 */

import { getFeedGenerator } from "./tools/rss/feed-generator.js";
import type { FeedGenerationOptions } from "./tools/rss/types.js";

const PORT = parseInt(process.env.RSS_PORT || "3001");
const generator = getFeedGenerator();

console.log(`🚀 Starting RSS HTTP Server on port ${PORT}`);

Bun.serve({
  port: PORT,
  fetch: async (req: Request): Promise<Response> => {
    const url = new URL(req.url);
    const pathname = url.pathname;
    const searchParams = url.searchParams;

    // Add CORS headers for all responses
    const corsHeaders = {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    };

    // Handle OPTIONS requests for CORS
    if (req.method === "OPTIONS") {
      return new Response(null, { status: 200, headers: corsHeaders });
    }

    // Only allow GET requests
    if (req.method !== "GET") {
      return new Response("Method Not Allowed", {
        status: 405,
        headers: { ...corsHeaders, "Allow": "GET, OPTIONS" }
      });
    }

    try {
      // Parse query parameters
      const options: FeedGenerationOptions = {
        limit: parseInt(searchParams.get("limit") || "20"),
        include_content: searchParams.get("include_content") !== "false",
        since: searchParams.get("since") || undefined
      };

      // Route handling
      if (pathname === "/feed" || pathname === "/") {
        // Main RSS feed
        return await handleRSSFeed(options, corsHeaders);
      }

      if (pathname.startsWith("/feed/")) {
        // Category-specific RSS feed
        const category = pathname.slice(6); // Remove "/feed/"
        return await handleRSSFeed({ ...options, category }, corsHeaders);
      }

      if (pathname === "/atom" || pathname.startsWith("/atom/")) {
        // Atom feed
        const category = pathname.startsWith("/atom/") ? pathname.slice(6) : undefined;
        return await handleAtomFeed({ ...options, category }, corsHeaders);
      }

      if (pathname === "/json" || pathname.startsWith("/json/")) {
        // JSON feed
        const category = pathname.startsWith("/json/") ? pathname.slice(6) : undefined;
        return await handleJSONFeed({ ...options, category }, corsHeaders);
      }

      if (pathname === "/stats") {
        // Feed statistics
        return await handleStats(searchParams.get("category") || undefined, corsHeaders);
      }

      if (pathname === "/health") {
        // Health check
        return new Response(JSON.stringify({
          status: "healthy",
          timestamp: new Date().toISOString(),
          server: "RSS Feed Server"
        }), {
          status: 200,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        });
      }

      // 404 for unknown routes
      return new Response("Not Found", {
        status: 404,
        headers: corsHeaders
      });

    } catch (error) {
      console.error("❌ RSS Server Error:", error);

      return new Response(JSON.stringify({
        error: "Internal Server Error",
        message: error instanceof Error ? error.message : "Unknown error"
      }), {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      });
    }
  },
});

/**
 * Handle RSS feed requests
 */
async function handleRSSFeed(
  options: FeedGenerationOptions,
  corsHeaders: Record<string, string>
): Promise<Response> {
  try {
    const rssContent = await generator.generateRSSFeed(options);

    return new Response(rssContent, {
      status: 200,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/rss+xml; charset=utf-8",
        "Cache-Control": "public, max-age=300" // 5 minutes cache
      }
    });
  } catch (error) {
    console.error("❌ RSS Feed Generation Error:", error);

    if (error instanceof Error && error.message.includes("not found")) {
      return new Response(`Category "${options.category}" not found`, {
        status: 404,
        headers: corsHeaders
      });
    }

    if (error instanceof Error && error.message.includes("No approved content")) {
      return new Response("No content available for feed", {
        status: 404,
        headers: corsHeaders
      });
    }

    throw error;
  }
}

/**
 * Handle Atom feed requests
 */
async function handleAtomFeed(
  options: FeedGenerationOptions,
  corsHeaders: Record<string, string>
): Promise<Response> {
  try {
    const atomContent = await generator.generateAtomFeed(options);

    return new Response(atomContent, {
      status: 200,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/atom+xml; charset=utf-8",
        "Cache-Control": "public, max-age=300"
      }
    });
  } catch (error) {
    console.error("❌ Atom Feed Generation Error:", error);

    if (error instanceof Error && error.message.includes("not found")) {
      return new Response(`Category "${options.category}" not found`, {
        status: 404,
        headers: corsHeaders
      });
    }

    throw error;
  }
}

/**
 * Handle JSON feed requests
 */
async function handleJSONFeed(
  options: FeedGenerationOptions,
  corsHeaders: Record<string, string>
): Promise<Response> {
  try {
    const jsonContent = await generator.generateJSONFeed(options);

    return new Response(jsonContent, {
      status: 200,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/feed+json; charset=utf-8",
        "Cache-Control": "public, max-age=300"
      }
    });
  } catch (error) {
    console.error("❌ JSON Feed Generation Error:", error);

    if (error instanceof Error && error.message.includes("not found")) {
      return new Response(`Category "${options.category}" not found`, {
        status: 404,
        headers: corsHeaders
      });
    }

    throw error;
  }
}

/**
 * Handle statistics requests
 */
async function handleStats(
  category: string | undefined,
  corsHeaders: Record<string, string>
): Promise<Response> {
  try {
    const stats = generator.getFeedStats(category);

    return new Response(JSON.stringify(stats, null, 2), {
      status: 200,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json; charset=utf-8",
        "Cache-Control": "public, max-age=60" // 1 minute cache for stats
      }
    });
  } catch (error) {
    console.error("❌ Stats Generation Error:", error);
    throw error;
  }
}

console.log(`✅ RSS HTTP Server running at http://localhost:${PORT}`);
console.log(`📡 Available endpoints:`);
console.log(`   GET /feed              - Main RSS feed`);
console.log(`   GET /feed/{category}   - Category RSS feed`);
console.log(`   GET /atom              - Main Atom feed`);
console.log(`   GET /atom/{category}   - Category Atom feed`);
console.log(`   GET /json              - Main JSON feed`);
console.log(`   GET /json/{category}   - Category JSON feed`);
console.log(`   GET /stats             - Feed statistics`);
console.log(`   GET /health            - Health check`);
console.log(`📝 Query parameters: ?limit=20&include_content=true&since=2024-01-01`);
